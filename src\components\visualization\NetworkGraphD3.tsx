import React, { useRef, useLayoutEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import * as d3 from 'd3';
import { RootState } from '../../store';
import { Box, Paper, Typography, Divider, FormControlLabel, Switch, Chip } from '@mui/material';
import { City, Connection } from '../../types';

// 自定义类型定义
interface NetworkNode extends d3.SimulationNodeDatum {
  id: number;
  name: string;
  description: string;
  radius: number;
  color: string;
}

interface NetworkLink extends d3.SimulationLinkDatum<NetworkNode> {
  source: NetworkNode | number;
  target: NetworkNode | number;
  cost: number;
  isActive: boolean;
  inMST: boolean;
}

// 错误边界组件
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(_: Error) {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('NetworkGraph错误边界捕获到错误:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <Paper sx={{ p: 2, height: '100%' }}>
          <Typography variant="h5" color="error">
            网络可视化组件出错
          </Typography>
          <Typography variant="body1">
            渲染网络图时发生错误，请尝试刷新页面或联系管理员。
          </Typography>
        </Paper>
      );
    }

    return this.props.children;
  }
}

/**
 * 网络图可视化组件 - 使用D3.js完全控制渲染
 */
const NetworkGraphD3: React.FC = () => {
  // 创建一个div容器的ref
  const containerRef = useRef<HTMLDivElement>(null);

  // 创建一个ref来存储D3.js的资源，以便在清理时使用
  const d3Resources = useRef<{
    simulation?: d3.Simulation<NetworkNode, NetworkLink> | null;
    svg?: d3.Selection<any, any, any, any> | null;
    zoom?: d3.ZoomBehavior<any, any> | null;
    resizeObserver?: ResizeObserver | null;
    mounted: boolean;
  }>({
    simulation: null,
    svg: null,
    zoom: null,
    resizeObserver: null,
    mounted: false
  });

  // 从Redux获取数据
  const cities = useSelector((state: RootState) => state.cities.items);
  const connections = useSelector((state: RootState) => state.connections.items);
  const mst = useSelector((state: RootState) => state.mst.currentMST);

  // 控制选项
  const [showMST, setShowMST] = useState<boolean>(true);
  const [showLabels, setShowLabels] = useState<boolean>(true);
  const [showWeights, setShowWeights] = useState<boolean>(true);
  const [hideInactive, setHideInactive] = useState<boolean>(false);

  // 使用useLayoutEffect确保DOM操作在浏览器绘制前完成
  useLayoutEffect(() => {
    try {
      // 如果没有容器或没有城市数据，则不渲染
      if (!containerRef.current) {
        console.log('容器引用为空，跳过渲染');
        return;
      }

      if (!cities || cities.length === 0) {
        console.log('没有城市数据，跳过渲染');
        return;
      }

    // 标记组件已挂载
    d3Resources.current.mounted = true;

    // 保存对d3Resources的引用，以便在清理函数中使用
    const resources = d3Resources.current;

    // 清理函数，在组件卸载或重新渲染前调用
    const cleanup = () => {
      try {
        // 停止仿真
        if (resources.simulation) {
          resources.simulation.stop();
          resources.simulation.on('tick', null);
          resources.simulation = null;
        }

        // 移除缩放行为
        if (resources.svg && resources.zoom) {
          resources.svg.on('.zoom', null);
          resources.zoom = null;
        }

        // 移除ResizeObserver
        if (resources.resizeObserver) {
          resources.resizeObserver.disconnect();
          resources.resizeObserver = null;
        }

        // 清除SVG内容
        if (containerRef.current && containerRef.current.firstChild) {
          try {
            containerRef.current.removeChild(containerRef.current.firstChild);
          } catch (error) {
            console.error('移除SVG时出错:', error);
            // 备选方案：使用innerHTML清空
            containerRef.current.innerHTML = '';
          }
        }

        // 重置D3资源
        resources.svg = null;
      } catch (error) {
        console.error('清理D3资源时出错:', error);
      }
    };

    // 先清理之前的资源
    cleanup();

    try {
      // 创建SVG元素
      const svg = d3.create('svg')
        .attr('width', '100%')
        .attr('height', '100%')
        .style('border', '1px solid #eee');

      // 将SVG添加到容器中
      containerRef.current.appendChild(svg.node()!);

      // 存储SVG引用
      d3Resources.current.svg = svg;

      // 获取容器大小
      const width = containerRef.current.clientWidth;
      const height = containerRef.current.clientHeight || 500;

      // 创建放大缩小和平移功能
      const zoom = d3.zoom<SVGSVGElement, unknown>()
        .scaleExtent([0.1, 4])
        .on('zoom', (event) => {
          g.attr('transform', event.transform);
        });

      // 存储缩放行为
      d3Resources.current.zoom = zoom;

      // 应用缩放行为
      svg.call(zoom as any);

      // 创建一个包含所有元素的组
      const g = svg.append('g');

      // 转换城市为节点
      const nodes: NetworkNode[] = cities.map((city: City) => ({
        ...city,
        radius: 20, // 节点半径
        color: '#1976d2', // 默认节点颜色
      }));

      // 检查连接是否在MST中
      const isInMST = (conn: Connection): boolean => {
        if (!mst) return false;
        return mst.edges.some(
          (mstConn: Connection) =>
            (mstConn.city1 === conn.city1 && mstConn.city2 === conn.city2) ||
            (mstConn.city1 === conn.city2 && mstConn.city2 === conn.city1)
        );
      };

      // 转换连接为边
      const links: NetworkLink[] = connections.map((conn) => ({
        source: conn.city1,
        target: conn.city2,
        cost: conn.cost,
        isActive: conn.isActive,
        inMST: isInMST(conn),
      })).filter(link => !hideInactive || link.isActive);

      // 创建力导向仿真
      const simulation = d3.forceSimulation<NetworkNode, NetworkLink>(nodes)
        .force('link', d3.forceLink<NetworkNode, NetworkLink>(links)
          .id((d) => d.id)
          .distance((d) => d.cost * 10 + 100) // 连接成本影响节点间距离
        )
        .force('charge', d3.forceManyBody().strength(-500))
        .force('center', d3.forceCenter(width / 2, height / 2))
        .force('collision', d3.forceCollide().radius((d) => (d as NetworkNode).radius + 10));

      // 存储仿真引用
      d3Resources.current.simulation = simulation;

      // 创建连接组
      const linkGroup = g.append('g').attr('class', 'links');

      // 绘制连接线
      const link = linkGroup
        .selectAll('line')
        .data(links)
        .join('line')
        .attr('stroke', (d) => {
          if (!d.isActive) return '#ccc'; // 禁用的连接
          return showMST && d.inMST ? '#4caf50' : '#999'; // MST中的连接为绿色，其他为灰色
        })
        .attr('stroke-width', (d) => showMST && d.inMST ? 3 : 1)
        .attr('stroke-dasharray', (d) => d.isActive ? 'none' : '5,5');

      // 创建连接标签组
      const linkLabelsGroup = g.append('g').attr('class', 'link-labels');

      // 添加连接权重标签
      if (showWeights) {
        linkLabelsGroup.selectAll('text')
          .data(links)
          .join('text')
          .attr('font-size', '10px')
          .attr('text-anchor', 'middle')
          .attr('dominant-baseline', 'middle')
          .attr('dy', -5)
          .attr('fill', (d) => d.isActive ? '#000' : '#888')
          .text((d) => d.cost.toFixed(1));
      }

      // 创建节点组
      const nodeGroup = g.append('g').attr('class', 'nodes');

      // 创建拖拽行为
      const dragBehavior = d3.drag<SVGCircleElement, NetworkNode>()
        .on('start', (event, d) => {
          if (!event.active) simulation.alphaTarget(0.3).restart();
          d.fx = d.x;
          d.fy = d.y;
        })
        .on('drag', (event, d) => {
          d.fx = event.x;
          d.fy = event.y;
        })
        .on('end', (event, d) => {
          if (!event.active) simulation.alphaTarget(0);
          d.fx = event.x;
          d.fy = event.y;
        });

      // 绘制节点
      const node = nodeGroup
        .selectAll('circle')
        .data(nodes)
        .join('circle')
        .attr('r', (d) => d.radius)
        .attr('fill', (d) => d.color)
        .call(dragBehavior as any);

      // 创建节点标签组
      const nodeLabelsGroup = g.append('g').attr('class', 'node-labels');

      // 添加城市名称标签
      if (showLabels) {
        nodeLabelsGroup.selectAll('text')
          .data(nodes)
          .join('text')
          .attr('font-size', '12px')
          .attr('text-anchor', 'middle')
          .attr('dy', 30)
          .text((d) => d.name);
      }

      // 仿真更新时重新定位元素
      simulation.on('tick', () => {
        // 更新连接位置
        link
          .attr('x1', (d) => (d.source as NetworkNode).x!)
          .attr('y1', (d) => (d.source as NetworkNode).y!)
          .attr('x2', (d) => (d.target as NetworkNode).x!)
          .attr('y2', (d) => (d.target as NetworkNode).y!);

        // 更新连接标签位置
        if (showWeights) {
          linkLabelsGroup.selectAll('text')
            .attr('x', (d: any) => {
              const link = d as NetworkLink;
              return ((link.source as NetworkNode).x! + (link.target as NetworkNode).x!) / 2;
            })
            .attr('y', (d: any) => {
              const link = d as NetworkLink;
              return ((link.source as NetworkNode).y! + (link.target as NetworkNode).y!) / 2;
            });
        }

        // 更新节点位置
        node
          .attr('cx', (d) => d.x!)
          .attr('cy', (d) => d.y!);

        // 更新节点标签位置
        if (showLabels) {
          nodeLabelsGroup.selectAll('text')
            .attr('x', (d: any) => (d as NetworkNode).x!)
            .attr('y', (d: any) => (d as NetworkNode).y!);
        }
      });

      // 创建ResizeObserver监听容器大小变化
      const resizeObserver = new ResizeObserver(() => {
        if (containerRef.current && resources.mounted) {
          const newWidth = containerRef.current.clientWidth;
          const newHeight = containerRef.current.clientHeight || 500;

          // 更新力导向仿真的中心力
          simulation.force('center', d3.forceCenter(newWidth / 2, newHeight / 2));
          simulation.alpha(0.3).restart(); // 重新启动仿真以应用新的中心力
        }
      });

      // 开始监听容器大小变化
      resizeObserver.observe(containerRef.current);

      // 存储ResizeObserver引用
      d3Resources.current.resizeObserver = resizeObserver;
    } catch (error) {
      console.error('渲染网络图时出错:', error);
    }

    // 组件卸载或重新渲染前清理资源
    return () => {
      // 标记组件已卸载
      resources.mounted = false;
      cleanup();
    };
    } catch (error) {
      console.error('NetworkGraphD3组件渲染出错:', error);
    }
  }, [cities, connections, mst, showMST, showLabels, showWeights, hideInactive]);

  return (
    <Paper sx={{ p: 2, height: '100%' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h5" component="h2">
          网络可视化
        </Typography>

        {mst && (
          <Box>
            <Chip
              label={`MST总成本: ${mst.totalCost.toFixed(2)}`}
              color="success"
              sx={{ mr: 1 }}
            />
            <Chip
              label={`算法: ${mst.algorithmType}`}
              color="primary"
              size="small"
            />
          </Box>
        )}
      </Box>

      <Divider sx={{ mb: 2 }} />

      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 2 }}>
        <FormControlLabel
          control={
            <Switch
              checked={showMST}
              onChange={(e) => setShowMST(e.target.checked)}
              color="primary"
            />
          }
          label="显示最小生成树"
        />

        <FormControlLabel
          control={
            <Switch
              checked={showLabels}
              onChange={(e) => setShowLabels(e.target.checked)}
              color="primary"
            />
          }
          label="显示城市标签"
        />

        <FormControlLabel
          control={
            <Switch
              checked={showWeights}
              onChange={(e) => setShowWeights(e.target.checked)}
              color="primary"
            />
          }
          label="显示连接成本"
        />

        <FormControlLabel
          control={
            <Switch
              checked={hideInactive}
              onChange={(e) => setHideInactive(e.target.checked)}
              color="primary"
            />
          }
          label="隐藏禁用连接"
        />
      </Box>

      <Box
        sx={{
          height: 'calc(100% - 120px)',
          minHeight: '400px',
          width: '100%',
          position: 'relative'
        }}
      >
        <div
          ref={containerRef}
          style={{
            width: '100%',
            height: '100%',
            position: 'absolute',
            top: 0,
            left: 0
          }}
        />
      </Box>

      <Typography variant="caption" color="textSecondary" sx={{ display: 'block', mt: 2 }}>
        提示: 拖动节点可以调整布局，滚轮可以缩放视图
      </Typography>
    </Paper>
  );
};

// 导出带错误边界的组件
const NetworkGraphWithErrorBoundary: React.FC = () => (
  <ErrorBoundary>
    <NetworkGraphD3 />
  </ErrorBoundary>
);

export default React.memo(NetworkGraphWithErrorBoundary);
