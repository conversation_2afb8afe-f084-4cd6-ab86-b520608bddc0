import React, { useRef, useEffect, useState, useCallback } from 'react';
import { useSelector } from 'react-redux';
import * as d3 from 'd3';
import { RootState } from '../../store';
import { Box, Paper, Typography, Divider, FormControlLabel, Switch, Chip } from '@mui/material';
import { City, Connection } from '../../types';

// 自定义类型定义
interface NetworkNode extends d3.SimulationNodeDatum {
  id: number;
  name: string;
  description: string;
  radius: number;
  color: string;
}

interface NetworkLink {
  source: NetworkNode;
  target: NetworkNode;
  cost: number;
  isActive: boolean;
  inMST: boolean;
}

// 错误边界组件
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(_: Error) {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('NetworkGraph错误边界捕获到错误:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <Paper sx={{ p: 2, height: '100%' }}>
          <Typography variant="h5" color="error">
            网络可视化组件出错
          </Typography>
          <Typography variant="body1">
            渲染网络图时发生错误，请尝试刷新页面或联系管理员。
          </Typography>
        </Paper>
      );
    }

    return this.props.children;
  }
}

/**
 * 网络图可视化组件 - 使用Canvas实现
 */
const NetworkGraphCanvas: React.FC = () => {
  // 创建Canvas引用
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // 创建一个ref来存储仿真和状态
  const simulationRef = useRef<{
    simulation: d3.Simulation<NetworkNode, d3.SimulationLinkDatum<NetworkNode>> | null;
    nodes: NetworkNode[];
    links: NetworkLink[];
    transform: d3.ZoomTransform;
    isDragging: boolean;
    dragNode: NetworkNode | null;
    width: number;
    height: number;
    animationFrameId: number | null;
  }>({
    simulation: null,
    nodes: [],
    links: [],
    transform: d3.zoomIdentity,
    isDragging: false,
    dragNode: null,
    width: 0,
    height: 0,
    animationFrameId: null
  });

  // 从Redux获取数据
  const cities = useSelector((state: RootState) => state.cities.items);
  const connections = useSelector((state: RootState) => state.connections.items);
  const mst = useSelector((state: RootState) => state.mst.currentMST);

  // 控制选项
  const [showMST, setShowMST] = useState<boolean>(true);
  const [showLabels, setShowLabels] = useState<boolean>(true);
  const [showWeights, setShowWeights] = useState<boolean>(true);
  const [hideInactive, setHideInactive] = useState<boolean>(false);

  // 准备节点和连接数据
  const prepareData = useCallback(() => {
    // 转换城市为节点
    const nodes: NetworkNode[] = cities.map((city: City) => ({
      id: city.id,
      name: city.name,
      description: city.description,
      radius: 20, // 节点半径
      color: '#1976d2', // 默认节点颜色
      x: Math.random() * 500,
      y: Math.random() * 500
    }));

    // 检查连接是否在MST中
    const isInMST = (conn: Connection): boolean => {
      if (!mst) return false;
      return mst.edges.some(
        (mstConn: Connection) =>
          (mstConn.city1 === conn.city1 && mstConn.city2 === conn.city2) ||
          (mstConn.city1 === conn.city2 && mstConn.city2 === conn.city1)
      );
    };

    // 创建节点映射，以便通过ID查找节点
    const nodeMap = new Map<number, NetworkNode>();
    nodes.forEach(node => nodeMap.set(node.id, node));

    // 转换连接为边
    const links: NetworkLink[] = connections
      .filter(conn => !hideInactive || conn.isActive)
      .map((conn) => ({
        source: nodeMap.get(conn.city1)!,
        target: nodeMap.get(conn.city2)!,
        cost: conn.cost,
        isActive: conn.isActive,
        inMST: isInMST(conn)
      }));

    return { nodes, links };
  }, [cities, connections, mst, hideInactive]);

  // 绘制Canvas
  const drawCanvas = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const context = canvas.getContext('2d');
    if (!context) return;

    const { width, height, transform, nodes, links } = simulationRef.current;

    // 清除Canvas
    context.clearRect(0, 0, width, height);
    context.save();

    // 应用缩放和平移
    context.translate(transform.x, transform.y);
    context.scale(transform.k, transform.k);

    // 绘制连接
    links.forEach(link => {
      context.beginPath();
      context.moveTo(link.source.x!, link.source.y!);
      context.lineTo(link.target.x!, link.target.y!);

      // 设置连接样式
      if (!link.isActive) {
        context.strokeStyle = '#ccc';
        context.setLineDash([5, 5]);
      } else if (showMST && link.inMST) {
        context.strokeStyle = '#4caf50';
        context.lineWidth = 3;
        context.setLineDash([]);
      } else {
        context.strokeStyle = '#999';
        context.lineWidth = 1;
        context.setLineDash([]);
      }

      context.stroke();
      context.setLineDash([]);
      context.lineWidth = 1;

      // 绘制连接权重
      if (showWeights) {
        const midX = (link.source.x! + link.target.x!) / 2;
        const midY = (link.source.y! + link.target.y!) / 2 - 5;

        context.fillStyle = link.isActive ? '#000' : '#888';
        context.font = '10px Arial';
        context.textAlign = 'center';
        context.textBaseline = 'middle';
        context.fillText(link.cost.toFixed(1), midX, midY);
      }
    });

    // 绘制节点
    nodes.forEach(node => {
      context.beginPath();
      context.arc(node.x!, node.y!, node.radius, 0, 2 * Math.PI);
      context.fillStyle = node.color;
      context.fill();

      // 绘制城市名称
      if (showLabels) {
        context.fillStyle = '#000';
        context.font = '12px Arial';
        context.textAlign = 'center';
        context.textBaseline = 'middle';
        context.fillText(node.name, node.x!, node.y! + 30);
      }
    });

    context.restore();
  }, [showMST, showLabels, showWeights]);

  // 处理缩放和平移
  const handleZoom = useCallback((event: WheelEvent) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    event.preventDefault();

    const { transform } = simulationRef.current;

    // 计算鼠标位置
    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // 计算缩放因子
    const delta = -event.deltaY;
    const scale = delta > 0 ? 1.1 : 0.9;

    // 更新变换
    const newTransform = transform.translate(
      x - scale * (x - transform.x),
      y - scale * (y - transform.y)
    ).scale(scale);

    simulationRef.current.transform = newTransform;
    drawCanvas();
  }, [drawCanvas]);

  // 处理鼠标按下事件
  const handleMouseDown = useCallback((event: MouseEvent) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const { transform, nodes } = simulationRef.current;

    // 计算鼠标位置
    const rect = canvas.getBoundingClientRect();
    const x = (event.clientX - rect.left - transform.x) / transform.k;
    const y = (event.clientY - rect.top - transform.y) / transform.k;

    // 检查是否点击了节点
    const node = nodes.find(n => {
      const dx = n.x! - x;
      const dy = n.y! - y;
      return Math.sqrt(dx * dx + dy * dy) <= n.radius;
    });

    if (node) {
      // 开始拖拽节点
      simulationRef.current.isDragging = true;
      simulationRef.current.dragNode = node;

      // 固定节点位置
      node.fx = node.x;
      node.fy = node.y;

      // 重启仿真
      if (simulationRef.current.simulation) {
        simulationRef.current.simulation.alphaTarget(0.3).restart();
      }
    } else {
      // 开始平移画布
      const startX = event.clientX;
      const startY = event.clientY;
      // Create a new transform with the same values instead of using clone
      const startTransform = d3.zoomIdentity.translate(transform.x, transform.y).scale(transform.k);

      const handleMouseMove = (moveEvent: MouseEvent) => {
        const dx = moveEvent.clientX - startX;
        const dy = moveEvent.clientY - startY;

        simulationRef.current.transform = startTransform.translate(dx, dy);
        drawCanvas();
      };

      // Store these functions in refs to ensure they're properly removed
      const mouseMoveHandler = (e: MouseEvent) => handleMouseMove(e);
      const mouseUpHandler = () => {
        document.removeEventListener('mousemove', mouseMoveHandler);
        document.removeEventListener('mouseup', mouseUpHandler);
      };

      document.addEventListener('mousemove', mouseMoveHandler);
      document.addEventListener('mouseup', mouseUpHandler);
    }
  }, [drawCanvas]);

  // 处理鼠标移动事件
  const handleMouseMove = useCallback((event: MouseEvent) => {
    const canvas = canvasRef.current;
    if (!canvas || !simulationRef.current.isDragging || !simulationRef.current.dragNode) return;

    const { transform, dragNode } = simulationRef.current;

    // 计算鼠标位置
    const rect = canvas.getBoundingClientRect();
    const x = (event.clientX - rect.left - transform.x) / transform.k;
    const y = (event.clientY - rect.top - transform.y) / transform.k;

    // 更新节点位置
    dragNode.fx = x;
    dragNode.fy = y;

    // 重启仿真
    if (simulationRef.current.simulation) {
      simulationRef.current.simulation.alpha(0.3).restart();
    }
  }, []);

  // 处理鼠标松开事件
  const handleMouseUp = useCallback(() => {
    if (!simulationRef.current.isDragging) return;

    simulationRef.current.isDragging = false;

    // 保持节点位置固定
    if (simulationRef.current.dragNode) {
      const dragNode = simulationRef.current.dragNode;
      dragNode.fx = dragNode.x;
      dragNode.fy = dragNode.y;
      simulationRef.current.dragNode = null;
    }

    // 停止仿真
    if (simulationRef.current.simulation) {
      simulationRef.current.simulation.alphaTarget(0);
    }
  }, []);

  // 初始化和更新仿真
  useEffect(() => {
    try {
      // 如果没有城市数据，则不渲染
      if (!cities || cities.length === 0) {
        console.log('没有城市数据，跳过渲染');
        return;
      }

      // 准备数据
      const { nodes, links } = prepareData();
      if (!nodes || nodes.length === 0) {
        console.log('节点数据为空，跳过渲染');
        return;
      }

      // 获取Canvas大小
      const canvas = canvasRef.current;
      if (!canvas) {
        console.log('Canvas引用为空，跳过渲染');
        return;
      }

    const width = canvas.width = canvas.clientWidth;
    const height = canvas.height = canvas.clientHeight;

    // 保存对simulationRef的引用，以便在清理函数中使用
    const simRef = simulationRef.current;

    // 更新仿真状态
    simRef.nodes = nodes;
    simRef.links = links;
    simRef.width = width;
    simRef.height = height;

    // 创建力导向仿真
    const simulation = d3.forceSimulation<NetworkNode>(nodes)
      .force('link', d3.forceLink<NetworkNode, d3.SimulationLinkDatum<NetworkNode>>(links as d3.SimulationLinkDatum<NetworkNode>[])
        .id((d) => d.id)
        .distance((d) => {
          const link = d as unknown as NetworkLink;
          return link.cost * 10 + 100; // 连接成本影响节点间距离
        })
      )
      .force('charge', d3.forceManyBody().strength(-500))
      .force('center', d3.forceCenter(width / 2, height / 2))
      .force('collision', d3.forceCollide().radius((d) => (d as NetworkNode).radius + 10));

    // 存储仿真引用
    simRef.simulation = simulation;

    // 仿真更新时重新绘制Canvas
    simulation.on('tick', () => {
      // 确保组件仍然挂载时才绘制
      if (canvasRef.current) {
        drawCanvas();
      }
    });

    // 添加事件监听器
    const wheelHandler = (e: WheelEvent) => handleZoom(e);
    const mouseDownHandler = (e: MouseEvent) => handleMouseDown(e);
    const mouseMoveHandler = (e: MouseEvent) => handleMouseMove(e);
    const mouseUpHandler = () => handleMouseUp();

    canvas.addEventListener('wheel', wheelHandler);
    canvas.addEventListener('mousedown', mouseDownHandler);
    document.addEventListener('mousemove', mouseMoveHandler);
    document.addEventListener('mouseup', mouseUpHandler);

    // 初始绘制
    drawCanvas();

    // 清理函数
    return () => {
      // 停止仿真
      if (simulation) {
        simulation.stop();
        simulation.on('tick', null);
      }

      // 移除事件监听器 - 使用相同的处理函数引用
      if (canvas) {
        canvas.removeEventListener('wheel', wheelHandler);
        canvas.removeEventListener('mousedown', mouseDownHandler);
      }
      document.removeEventListener('mousemove', mouseMoveHandler);
      document.removeEventListener('mouseup', mouseUpHandler);

      // 取消动画帧
      if (simRef.animationFrameId !== null) {
        cancelAnimationFrame(simRef.animationFrameId);
        simRef.animationFrameId = null;
      }

      // 重置拖拽状态
      simRef.isDragging = false;
      simRef.dragNode = null;
    };
    } catch (error) {
      console.error('NetworkGraphCanvas组件渲染出错:', error);
    }
  }, [cities, connections, mst, showMST, showLabels, showWeights, hideInactive, prepareData, drawCanvas, handleZoom, handleMouseDown, handleMouseMove, handleMouseUp]);

  return (
    <Paper sx={{ p: 2, height: '100%' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h5" component="h2">
          网络可视化 (Canvas)
        </Typography>

        {mst && (
          <Box>
            <Chip
              label={`MST总成本: ${mst.totalCost.toFixed(2)}`}
              color="success"
              sx={{ mr: 1 }}
            />
            <Chip
              label={`算法: ${mst.algorithmType}`}
              color="primary"
              size="small"
            />
          </Box>
        )}
      </Box>

      <Divider sx={{ mb: 2 }} />

      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 2 }}>
        <FormControlLabel
          control={
            <Switch
              checked={showMST}
              onChange={(e) => setShowMST(e.target.checked)}
              color="primary"
            />
          }
          label="显示最小生成树"
        />

        <FormControlLabel
          control={
            <Switch
              checked={showLabels}
              onChange={(e) => setShowLabels(e.target.checked)}
              color="primary"
            />
          }
          label="显示城市标签"
        />

        <FormControlLabel
          control={
            <Switch
              checked={showWeights}
              onChange={(e) => setShowWeights(e.target.checked)}
              color="primary"
            />
          }
          label="显示连接成本"
        />

        <FormControlLabel
          control={
            <Switch
              checked={hideInactive}
              onChange={(e) => setHideInactive(e.target.checked)}
              color="primary"
            />
          }
          label="隐藏禁用连接"
        />
      </Box>

      <Box
        sx={{
          height: 'calc(100% - 120px)',
          minHeight: '400px',
          width: '100%',
          position: 'relative'
        }}
      >
        <canvas
          ref={canvasRef}
          style={{
            width: '100%',
            height: '100%',
            display: 'block',
            border: '1px solid #eee'
          }}
        />
      </Box>

      <Typography variant="caption" color="textSecondary" sx={{ display: 'block', mt: 2 }}>
        提示: 拖动节点可以调整布局，滚轮可以缩放视图，拖动空白区域可以平移视图
      </Typography>
    </Paper>
  );
};

// 导出带错误边界的组件
const NetworkGraphCanvasWithErrorBoundary: React.FC = () => (
  <ErrorBoundary>
    <NetworkGraphCanvas />
  </ErrorBoundary>
);

export default React.memo(NetworkGraphCanvasWithErrorBoundary);
