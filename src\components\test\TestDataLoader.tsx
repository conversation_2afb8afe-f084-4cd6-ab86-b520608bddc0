import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { Button, Snackbar, Box, Typography, Divider } from '@mui/material';
import { addCities, clearCities } from '../../store/citySlice';
import { addConnection, clearConnections } from '../../store/connectionSlice';
import { generateTestCities, generateTestConnections } from '../../utils/testData';

/**
 * 测试数据加载组件
 */
const TestDataLoader: React.FC = () => {
  const dispatch = useDispatch();
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  // 加载测试城市
  const handleLoadTestCities = () => {
    try {
      const testCities = generateTestCities();
      dispatch(clearCities());
      dispatch(addCities(testCities));
      setSnackbarMessage(`已加载${testCities.length}个测试城市`);
      setSnackbarOpen(true);
    } catch (error) {
      console.error('加载测试城市失败:', error);
      setSnackbarMessage('加载测试城市失败');
      setSnackbarOpen(true);
    }
  };

  // 加载测试连接
  const handleLoadTestConnections = () => {
    try {
      const testConnections = generateTestConnections();
      dispatch(clearConnections());
      
      // 逐个添加连接，以便触发ID分配
      testConnections.forEach(conn => {
        dispatch(addConnection(conn));
      });
      
      setSnackbarMessage(`已加载${testConnections.length}个测试连接`);
      setSnackbarOpen(true);
    } catch (error) {
      console.error('加载测试连接失败:', error);
      setSnackbarMessage('加载测试连接失败');
      setSnackbarOpen(true);
    }
  };

  // 加载所有测试数据
  const handleLoadAllTestData = () => {
    try {
      handleLoadTestCities();
      setTimeout(() => {
        handleLoadTestConnections();
      }, 100); // 短暂延迟，确保城市先加载完成
    } catch (error) {
      console.error('加载测试数据失败:', error);
      setSnackbarMessage('加载测试数据失败');
      setSnackbarOpen(true);
    }
  };

  // 清空所有数据
  const handleClearAllData = () => {
    try {
      dispatch(clearCities());
      dispatch(clearConnections());
      setSnackbarMessage('已清空所有数据');
      setSnackbarOpen(true);
    } catch (error) {
      console.error('清空数据失败:', error);
      setSnackbarMessage('清空数据失败');
      setSnackbarOpen(true);
    }
  };

  // 关闭提示消息
  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  return (
    <Box sx={{ mb: 3, p: 2, border: '1px dashed #ccc', borderRadius: 1 }}>
      <Typography variant="h6" gutterBottom>
        测试工具
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
        用于快速加载测试数据，方便功能测试
      </Typography>
      <Divider sx={{ mb: 2 }} />
      <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
        <Button 
          variant="outlined" 
          color="primary" 
          onClick={handleLoadTestCities}
          size="small"
        >
          加载测试城市
        </Button>
        <Button 
          variant="outlined" 
          color="primary" 
          onClick={handleLoadTestConnections}
          size="small"
        >
          加载测试连接
        </Button>
        <Button 
          variant="contained" 
          color="primary" 
          onClick={handleLoadAllTestData}
          size="small"
        >
          加载所有测试数据
        </Button>
        <Button 
          variant="outlined" 
          color="error" 
          onClick={handleClearAllData}
          size="small"
        >
          清空所有数据
        </Button>
      </Box>
      
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={handleCloseSnackbar}
        message={snackbarMessage}
      />
    </Box>
  );
};

export default TestDataLoader;
