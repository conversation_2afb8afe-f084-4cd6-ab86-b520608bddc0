/* 全局样式 */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', '思源黑体', 'Microsoft YaHei',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* 防止浏览器翻译 */
.notranslate {
  -webkit-translate: none;
  translate: none;
}

[translate="no"] {
  -webkit-translate: none;
  translate: none;
}

[lang="zh-CN"] {
  -webkit-translate: none;
  translate: none;
}
