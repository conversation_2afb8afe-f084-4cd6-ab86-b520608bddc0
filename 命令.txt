启动步骤回顾
环境准备：确保安装了Node.js和npm

导航到项目目录：使用cd命令进入项目文件夹 	 cd /d D:\Users\jsxzxhx\Desktop\数据结构\project\mst-network-optimizer
安装依赖：运行				 npm install
启动项目：运行				 npm start
访问项目：在浏览器中访问			 http://localhost:3000（或系统分配的其他端口）


可能遇到的问题
端口占用：如果3000端口被占用，系统会询问是否使用另一个端口
依赖安装问题：如果遇到依赖冲突，可以尝试npm install --legacy-peer-deps
网络问题：如果依赖下载缓慢，可以考虑使用国内npm镜像
测试功能
项目启动后，您可以测试以下功能：
测试数据加载：使用首页上的"测试工具"加载测试数据
城市管理：添加、编辑和删除城市
连接管理：添加、编辑、切换状态和删除连接
MST计算：使用Kruskal或Prim算法计算最小生成树
网络可视化：查看网络图和最小生成树
这个项目是一个基于React的前端应用，用于通信网络最小生成树优化。它实现了Kruskal和Prim算法，并提供了直观的可视化界面，帮助用户理解和分析最小生成树。