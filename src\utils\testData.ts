import { City, Connection } from '../types';

/**
 * 生成测试城市数据
 * @returns 城市数组
 */
export const generateTestCities = (): Omit<City, 'id'>[] => {
  return [
    { name: '北京', description: '中国首都' },
    { name: '上海', description: '中国最大城市' },
    { name: '广州', description: '华南地区中心城市' },
    { name: '深圳', description: '中国特区城市' },
    { name: '成都', description: '西南地区中心城市' },
    { name: '武汉', description: '华中地区中心城市' },
    { name: '西安', description: '西北地区中心城市' },
    { name: '南京', description: '华东地区中心城市' },
    { name: '杭州', description: '浙江省省会' },
    { name: '重庆', description: '中国直辖市' }
  ];
};

/**
 * 生成测试连接数据
 * @returns 连接数组
 */
export const generateTestConnections = (): Omit<Connection, 'id'>[] => {
  return [
    { city1: 0, city2: 1, cost: 150, isActive: true },  // 北京-上海
    { city1: 0, city2: 2, cost: 300, isActive: true },  // 北京-广州
    { city1: 0, city2: 5, cost: 120, isActive: true },  // 北京-武汉
    { city1: 0, city2: 6, cost: 180, isActive: true },  // 北京-西安
    { city1: 1, city2: 2, cost: 200, isActive: true },  // 上海-广州
    { city1: 1, city2: 3, cost: 140, isActive: true },  // 上海-深圳
    { city1: 1, city2: 7, cost: 80, isActive: true },   // 上海-南京
    { city1: 1, city2: 8, cost: 60, isActive: true },   // 上海-杭州
    { city1: 2, city2: 3, cost: 50, isActive: true },   // 广州-深圳
    { city1: 2, city2: 4, cost: 250, isActive: true },  // 广州-成都
    { city1: 2, city2: 5, cost: 180, isActive: true },  // 广州-武汉
    { city1: 3, city2: 9, cost: 320, isActive: true },  // 深圳-重庆
    { city1: 4, city2: 6, cost: 130, isActive: true },  // 成都-西安
    { city1: 4, city2: 9, cost: 90, isActive: true },   // 成都-重庆
    { city1: 5, city2: 7, cost: 110, isActive: true },  // 武汉-南京
    { city1: 6, city2: 9, cost: 200, isActive: true },  // 西安-重庆
    { city1: 7, city2: 8, cost: 70, isActive: true },   // 南京-杭州
    { city1: 8, city2: 9, cost: 280, isActive: true }   // 杭州-重庆
  ];
};
