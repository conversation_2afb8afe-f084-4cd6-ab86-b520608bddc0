import React from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { Box, CssBaseline } from '@mui/material';
import MainLayout from './components/layout/MainLayout';
import HomePage from './pages/HomePage';
import CityManager from './components/city/CityManager';
import ConnectionManager from './components/connection/ConnectionManager';
import MSTCalculator from './components/mst/MSTCalculator';
import NetworkGraph from './components/visualization/NetworkGraph';

/**
 * 应用程序主组件
 */
const AppMain: React.FC = () => {
  return (
    <BrowserRouter>
      <Box
        sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}
        lang="zh-CN"
        className="notranslate"
        translate="no"
      >
        <CssBaseline />
        <MainLayout>
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/cities" element={<CityManager />} />
            <Route path="/connections" element={<ConnectionManager />} />
            <Route path="/mst" element={<MSTCalculator />} />
            <Route path="/visualization" element={<NetworkGraph />} />
          </Routes>
        </MainLayout>
      </Box>
    </BrowserRouter>
  );
};

export default AppMain;
