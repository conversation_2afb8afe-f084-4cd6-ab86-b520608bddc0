{"name": "mst-network-optimizer", "version": "1.0.0", "description": "通信网络最小生成树优化系统", "private": true, "dependencies": {"@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@mui/icons-material": "^5.14.10", "@mui/material": "^5.14.10", "@reduxjs/toolkit": "^1.9.5", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/d3": "^7.4.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.38", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "d3": "^7.8.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^8.1.1", "react-router-dom": "^6.14.2", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0"}}