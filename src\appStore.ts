import { configureStore } from '@reduxjs/toolkit';
import cityReducer from './store/citySlice';
import connectionReducer from './store/connectionSlice';
import mstReducer from './store/mstSlice';

// 配置Redux Store
export const store = configureStore({
  reducer: {
    cities: cityReducer,
    connections: connectionReducer,
    mst: mstReducer,
  },
});

// 导出类型
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
